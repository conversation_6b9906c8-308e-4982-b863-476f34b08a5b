2025-08-01 23:53:10,439 - __main__ - INFO - Quotex client initialized successfully.
2025-08-01 23:53:25,894 - __main__ - INFO - Quotex client initialized successfully.
2025-08-01 23:53:28,903 - __main__ - INFO - Establishing connection...
2025-08-01 23:53:28,904 - __main__ - INFO - Attempting to connect to Quotex API...
2025-08-01 23:53:52,921 - websocket - INFO - Websocket connected
2025-08-01 23:53:52,977 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-01 23:53:55,507 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-08-01 23:53:55,507 - __main__ - INFO - Running connection test.
2025-08-01 23:53:57,510 - __main__ - INFO - Connection test successful.
2025-08-01 23:53:59,552 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-02 02:14:55,371 - __main__ - INFO - Quotex client initialized successfully.
2025-08-02 02:17:48,287 - __main__ - INFO - Quotex client initialized successfully.
2025-08-02 02:17:51,321 - __main__ - INFO - Establishing connection...
2025-08-02 02:17:51,321 - __main__ - INFO - Attempting to connect to Quotex API...
2025-08-02 02:17:53,208 - websocket - INFO - Websocket connected
2025-08-02 02:17:53,257 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-02 02:17:56,301 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-08-02 02:17:56,301 - __main__ - INFO - Getting candles for GBPUSD with period of 300s.
2025-08-02 02:17:56,628 - __main__ - INFO - Retrieved 199 candles.
2025-08-02 02:17:58,826 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 08:19:48,877 - __main__ - INFO - Quotex client initialized successfully.
2025-08-07 08:19:51,920 - __main__ - INFO - Establishing connection...
2025-08-07 08:19:51,921 - __main__ - INFO - Attempting to connect to Quotex API...
2025-08-07 08:19:54,097 - websocket - INFO - Websocket connected
2025-08-07 08:19:54,161 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 08:19:56,699 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-08-07 08:19:56,699 - __main__ - INFO - Executing simple buy: 2.0 on AUDCHF_otc in put direction for 60s.
2025-08-07 08:19:58,947 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 08:20:00,038 - __main__ - CRITICAL - Unexpected error occurred during command execution: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\pyquotex-master\app.py", line 583, in main
    await cli.buy_simple(args.amount, args.asset, args.direction, args.duration)
  File "D:\pyquotex-master\app.py", line 95, in wrapper
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pyquotex-master\app.py", line 222, in buy_simple
    await self.client.change_account("PRACTICE")
TypeError: object NoneType can't be used in 'await' expression
2025-08-07 08:22:17,308 - __main__ - INFO - Quotex client initialized successfully.
2025-08-07 08:22:44,912 - __main__ - INFO - Quotex client initialized successfully.
2025-08-07 08:22:47,935 - __main__ - INFO - Establishing connection...
2025-08-07 08:22:47,936 - __main__ - INFO - Attempting to connect to Quotex API...
2025-08-07 08:22:51,387 - websocket - INFO - Websocket connected
2025-08-07 08:22:51,462 - pyquotex.ws.client - INFO - Websocket client connected.
2025-08-07 08:22:53,984 - __main__ - INFO - Connected successfully: Websocket connected successfully!!!
2025-08-07 08:22:53,985 - __main__ - INFO - Executing simple buy: 100.0 on EURUSD_otc in call direction for 60s.
2025-08-07 08:22:56,191 - pyquotex.ws.client - INFO - Websocket connection closed.
2025-08-07 08:22:57,199 - __main__ - CRITICAL - Unexpected error occurred during command execution: object NoneType can't be used in 'await' expression
Traceback (most recent call last):
  File "D:\pyquotex-master\app.py", line 583, in main
    await cli.buy_simple(args.amount, args.asset, args.direction, args.duration)
  File "D:\pyquotex-master\app.py", line 95, in wrapper
    result = await func(self, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\pyquotex-master\app.py", line 222, in buy_simple
    await self.client.change_account("PRACTICE")
TypeError: object NoneType can't be used in 'await' expression
